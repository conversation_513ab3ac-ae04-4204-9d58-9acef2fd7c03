class AppConfig {
  // معلومات التطبيق
  static const String appName = 'تطبيق الشريعة والقانون';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'عارض PDF للمواد الدراسية';

  // إعدادات الأنيميشن
  static const Duration defaultAnimationDuration = Duration(milliseconds: 300);
  static const Duration longAnimationDuration = Duration(milliseconds: 600);
  static const Duration shortAnimationDuration = Duration(milliseconds: 150);

  // إعدادات PDF
  static const double defaultZoomLevel = 1.0;
  static const double maxZoomLevel = 3.0;
  static const double minZoomLevel = 0.5;
  static const double zoomStep = 0.25;

  // إعدادات التخزين المحلي
  static const String prefsKeyTheme = 'app_theme';
  static const String prefsKeyLanguage = 'app_language';
  static const String prefsKeyFontSize = 'font_size';

  // مسارات الملفات
  static const String assetsPath = 'assets/';
  static const String imagesPath = 'assets/images/';
  static const String iconsPath = 'assets/icons/';
  static const String pdfsPath = 'assets/pdfs/';
  static const String animationsPath = 'assets/animations/';

  // أحجام الخطوط
  static const double fontSizeSmall = 12.0;
  static const double fontSizeMedium = 14.0;
  static const double fontSizeLarge = 16.0;
  static const double fontSizeXLarge = 18.0;
  static const double fontSizeXXLarge = 20.0;

  // المسافات والأبعاد
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;

  static const double marginSmall = 8.0;
  static const double marginMedium = 16.0;
  static const double marginLarge = 24.0;
  static const double marginXLarge = 32.0;

  static const double borderRadiusSmall = 8.0;
  static const double borderRadiusMedium = 12.0;
  static const double borderRadiusLarge = 16.0;
  static const double borderRadiusXLarge = 20.0;

  // أحجام الأيقونات
  static const double iconSizeSmall = 16.0;
  static const double iconSizeMedium = 24.0;
  static const double iconSizeLarge = 32.0;
  static const double iconSizeXLarge = 48.0;

  // إعدادات الشبكة
  static const Duration networkTimeout = Duration(seconds: 30);
  static const int maxRetries = 3;

  // إعدادات التطبيق
  static const bool enableDebugMode = true;
  static const bool enableAnalytics = false;
  static const bool enableCrashReporting = false;

  // معلومات المطور
  static const String developerName = 'فريق التطوير';
  static const String developerEmail = '<EMAIL>';
  static const String supportEmail = '<EMAIL>';

  // روابط مهمة
  static const String privacyPolicyUrl = 'https://example.com/privacy';
  static const String termsOfServiceUrl = 'https://example.com/terms';
  static const String helpUrl = 'https://example.com/help';

  // إعدادات الإشعارات
  static const bool enableNotifications = true;
  static const bool enableSoundNotifications = true;
  static const bool enableVibrationNotifications = true;

  // إعدادات الأمان
  static const bool enableBiometricAuth = false;
  static const bool enablePinAuth = false;
  static const int maxLoginAttempts = 5;

  // إعدادات التخزين المؤقت
  static const Duration cacheExpiration = Duration(hours: 24);
  static const int maxCacheSize = 100; // MB

  // إعدادات الأداء
  static const int maxConcurrentDownloads = 3;
  static const int imageQuality = 85;
  static const bool enableImageCompression = true;

  // إعدادات إمكانية الوصول
  static const bool enableHighContrast = false;
  static const bool enableLargeText = false;
  static const bool enableScreenReader = false;

  // إعدادات التجربة
  static const bool enableHapticFeedback = true;
  static const bool enableSmoothScrolling = true;
  static const bool enableParallaxEffect = true;

  // معلومات الجامعة
  static const String universityName = 'جامعة الأزهر الشريف';
  static const String facultyName = 'منصة الشريعة والقانون';
  static const String facultyLocation = 'القاهرة، مصر';

  // معلومات الاتصال
  static const String facultyPhone = '+20-2-12345678';
  static const String facultyEmail = '<EMAIL>';
  static const String facultyWebsite = 'https://www.azhar.edu.eg';
}
