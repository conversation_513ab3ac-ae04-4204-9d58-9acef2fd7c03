{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\20223\\2025\\legl92025\\android\\app\\.cxx\\Debug\\443u4q56\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\20223\\2025\\legl92025\\android\\app\\.cxx\\Debug\\443u4q56\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}