import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/post_model.dart';
import '../providers/theme_provider.dart';
import '../services/post_service.dart';

class CommentWidget extends StatefulWidget {
  final CommentModel comment;
  final String postId;
  final ThemeProvider themeProvider;

  const CommentWidget({
    super.key,
    required this.comment,
    required this.postId,
    required this.themeProvider,
  });

  @override
  State<CommentWidget> createState() => _CommentWidgetState();
}

class _CommentWidgetState extends State<CommentWidget> {
  bool _isLiked = false;
  bool _showReplies = false;
  final TextEditingController _replyController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _checkIfLiked();
  }

  void _checkIfLiked() {
    // Check if current user liked this comment
    // We'll check if current user ID is in the likedBy list
    setState(() {
      // For now, we'll set it to false since we don't have current user ID
      // In a real app, you would check: widget.comment.likedBy.contains(currentUserId)
      _isLiked = false;
    });
  }

  @override
  void dispose() {
    _replyController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 16,
                backgroundColor: const Color(0xFF10B981),
                child: Text(
                  widget.comment.authorName[0],
                  style: GoogleFonts.cairo(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color:
                            widget.themeProvider.isDarkMode
                                ? const Color(0xFF0F172A)
                                : Colors.grey[100],
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.comment.authorName,
                            style: GoogleFonts.cairo(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: const Color(0xFF10B981),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            widget.comment.comment,
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              color:
                                  widget.themeProvider.isDarkMode
                                      ? Colors.white
                                      : Colors.black87,
                            ),
                            textDirection: TextDirection.rtl,
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Text(
                          _formatTime(widget.comment.createdAt),
                          style: GoogleFonts.cairo(
                            fontSize: 11,
                            color:
                                widget.themeProvider.isDarkMode
                                    ? Colors.grey[400]
                                    : Colors.grey[600],
                          ),
                        ),
                        const SizedBox(width: 16),
                        GestureDetector(
                          onTap: _toggleLike,
                          child: Row(
                            children: [
                              Icon(
                                _isLiked
                                    ? Icons.favorite
                                    : Icons.favorite_border,
                                size: 16,
                                color: _isLiked ? Colors.red : Colors.grey[500],
                              ),
                              if (widget.comment.likes > 0) ...[
                                const SizedBox(width: 4),
                                Text(
                                  '${widget.comment.likes}',
                                  style: GoogleFonts.cairo(
                                    fontSize: 11,
                                    color:
                                        widget.themeProvider.isDarkMode
                                            ? Colors.grey[400]
                                            : Colors.grey[600],
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),
                        const SizedBox(width: 16),
                        GestureDetector(
                          onTap: () {
                            setState(() {
                              _showReplies = !_showReplies;
                            });
                          },
                          child: Text(
                            'رد',
                            style: GoogleFonts.cairo(
                              fontSize: 11,
                              fontWeight: FontWeight.bold,
                              color: const Color(0xFF10B981),
                            ),
                          ),
                        ),
                        if (widget.comment.replies.isNotEmpty) ...[
                          const SizedBox(width: 16),
                          GestureDetector(
                            onTap: () {
                              setState(() {
                                _showReplies = !_showReplies;
                              });
                            },
                            child: Text(
                              '${widget.comment.replies.length} رد',
                              style: GoogleFonts.cairo(
                                fontSize: 11,
                                color:
                                    widget.themeProvider.isDarkMode
                                        ? Colors.grey[400]
                                        : Colors.grey[600],
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (_showReplies) ...[
            const SizedBox(height: 12),
            Container(
              margin: const EdgeInsets.only(right: 28),
              child: Column(
                children: [
                  // Reply input
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 12,
                        backgroundColor: const Color(0xFF10B981),
                        child: Text(
                          'أ',
                          style: GoogleFonts.cairo(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: TextField(
                          controller: _replyController,
                          textDirection: TextDirection.rtl,
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            color:
                                widget.themeProvider.isDarkMode
                                    ? Colors.white
                                    : Colors.black87,
                          ),
                          decoration: InputDecoration(
                            hintText: 'اكتب رداً...',
                            hintStyle: GoogleFonts.cairo(
                              fontSize: 12,
                              color:
                                  widget.themeProvider.isDarkMode
                                      ? Colors.grey[400]
                                      : Colors.grey[500],
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(16),
                              borderSide: BorderSide(
                                color:
                                    widget.themeProvider.isDarkMode
                                        ? Colors.grey[600]!
                                        : Colors.grey[300]!,
                              ),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(16),
                              borderSide: BorderSide(
                                color:
                                    widget.themeProvider.isDarkMode
                                        ? Colors.grey[600]!
                                        : Colors.grey[300]!,
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(16),
                              borderSide: const BorderSide(
                                color: Color(0xFF10B981),
                                width: 2,
                              ),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 8,
                            ),
                            suffixIcon: IconButton(
                              onPressed: _addReply,
                              icon: const Icon(
                                Icons.send,
                                color: Color(0xFF10B981),
                                size: 16,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  // شريط عرض الردود (مخفية بشكل افتراضي)
                  if (widget.comment.replies.isNotEmpty) ...[
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          _showReplies = !_showReplies;
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          children: [
                            Container(
                              width: 20,
                              height: 1,
                              color:
                                  widget.themeProvider.isDarkMode
                                      ? const Color(0xFF6B7280)
                                      : const Color(0xFF9CA3AF),
                            ),
                            const SizedBox(width: 8),
                            Icon(
                              _showReplies
                                  ? Icons.keyboard_arrow_up
                                  : Icons.keyboard_arrow_down,
                              size: 16,
                              color:
                                  widget.themeProvider.isDarkMode
                                      ? const Color(0xFF9CA3AF)
                                      : const Color(0xFF6B7280),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              _showReplies
                                  ? 'إخفاء الردود'
                                  : 'عرض ${widget.comment.replies.length} ${widget.comment.replies.length == 1 ? 'رد' : 'ردود'}',
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                color:
                                    widget.themeProvider.isDarkMode
                                        ? const Color(0xFF9CA3AF)
                                        : const Color(0xFF6B7280),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                  // عرض الردود فقط إذا كانت مفتوحة
                  if (_showReplies)
                    ...widget.comment.replies.map(
                      (reply) => Container(
                        margin: const EdgeInsets.only(right: 32, top: 8),
                        child: CommentWidget(
                          comment: reply,
                          postId: widget.postId,
                          themeProvider: widget.themeProvider,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  String _formatTime(DateTime? dateTime) {
    if (dateTime == null) return 'الآن';

    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return 'منذ ${difference.inMinutes} د';
    } else if (difference.inDays < 1) {
      return 'منذ ${difference.inHours} س';
    } else {
      return 'منذ ${difference.inDays} ي';
    }
  }

  Future<void> _toggleLike() async {
    setState(() {
      _isLiked = !_isLiked;
    });

    if (_isLiked) {
      HapticFeedback.lightImpact();
    }

    // Implement comment like functionality
    // In a real app, you would call an API to like/unlike the comment
    // For now, we just update the local state
    // Example: await PostService.toggleCommentLike(widget.comment.id);
  }

  Future<void> _addReply() async {
    final reply = _replyController.text.trim();
    if (reply.isEmpty) return;

    final success = await PostService.addComment(
      postId: widget.postId,
      comment: reply,
      parentCommentId: widget.comment.id,
    );

    if (success) {
      _replyController.clear();
      // Refresh parent widget to show new reply
      // In a real app, you would call a callback function or use state management
      // Example: widget.onReplyAdded?.call();
    }
  }
}
