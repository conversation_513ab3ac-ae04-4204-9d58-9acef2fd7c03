import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import '../models/pdf_model.dart';
import '../services/enhanced_pdf_service.dart';

/// Dialog تحميل محسن مع تحميل حقيقي وتحسينات شاملة
class EnhancedDownloadDialog extends StatefulWidget {
  final PDFModel pdf;
  final Function(double)? onProgress;
  final VoidCallback? onComplete;
  final Function(String)? onError;
  final VoidCallback? onCancel;

  const EnhancedDownloadDialog({
    super.key,
    required this.pdf,
    this.onProgress,
    this.onComplete,
    this.onError,
    this.onCancel,
  });

  @override
  State<EnhancedDownloadDialog> createState() => _EnhancedDownloadDialogState();
}

class _EnhancedDownloadDialogState extends State<EnhancedDownloadDialog>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _scaleController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;

  double _progress = 0.0;
  bool _isCompleted = false;
  bool _isCancelled = false;
  String _statusMessage = 'بدء التحميل...';
  String _downloadSpeed = '';
  String _downloadedSize = '';
  int _startTime = 0;
  bool _showCancelButton = true;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startDownload();
  }

  void _setupAnimations() {
    _rotationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _rotationAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.linear),
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.elasticOut),
    );

    _rotationController.repeat();
    _scaleController.forward();
  }

  void _startDownload() {
    _startTime = DateTime.now().millisecondsSinceEpoch;
    _startRealDownload();
  }

  /// بدء التحميل الحقيقي مع تتبع التقدم
  Future<void> _startRealDownload() async {
    try {
      _updateProgress(0.0, 'بدء الاتصال بالخادم...');

      // بدء التحميل الحقيقي
      final filePath = await EnhancedPDFService.downloadPDFToLocal(
        url: widget.pdf.url,
        fileName: widget.pdf.name,
        onProgress: (progress) {
          if (!_isCancelled && mounted) {
            _updateRealProgress(progress);
          }
        },
      );

      if (!_isCancelled && mounted) {
        if (filePath != null) {
          _completeDownload();
        } else {
          _failDownload('فشل في تحميل الملف');
        }
      }
    } catch (e) {
      if (!_isCancelled && mounted) {
        _failDownload('خطأ في التحميل: $e');
      }
    }
  }

  /// تحديث التقدم الحقيقي مع حساب السرعة
  void _updateRealProgress(double progress) {
    if (mounted) {
      final currentTime = DateTime.now().millisecondsSinceEpoch;
      final elapsedTime = (currentTime - _startTime) / 1000; // بالثواني

      // حساب السرعة التقريبية
      if (elapsedTime > 0 && progress > 0) {
        final estimatedTotalSize = 5 * 1024 * 1024; // تقدير 5 MB
        final downloadedBytes = (progress * estimatedTotalSize);
        final speedBytesPerSecond = downloadedBytes / elapsedTime;

        if (speedBytesPerSecond > 1024 * 1024) {
          _downloadSpeed =
              '${(speedBytesPerSecond / (1024 * 1024)).toStringAsFixed(1)} MB/s';
        } else if (speedBytesPerSecond > 1024) {
          _downloadSpeed =
              '${(speedBytesPerSecond / 1024).toStringAsFixed(1)} KB/s';
        } else {
          _downloadSpeed = '${speedBytesPerSecond.toStringAsFixed(0)} B/s';
        }

        _downloadedSize =
            '${(downloadedBytes / (1024 * 1024)).toStringAsFixed(1)} MB';
      }

      setState(() {
        _progress = progress;
        if (progress < 0.1) {
          _statusMessage = 'الاتصال بالخادم...';
        } else if (progress < 0.3) {
          _statusMessage = 'بدء التحميل...';
        } else if (progress < 0.9) {
          _statusMessage = 'تحميل البيانات... $_downloadSpeed';
        } else {
          _statusMessage = 'حفظ الملف...';
        }
      });

      widget.onProgress?.call(progress);
    }
  }

  /// فشل التحميل
  void _failDownload(String error) {
    if (mounted) {
      setState(() {
        _statusMessage = error;
        _showCancelButton = false;
      });

      _rotationController.stop();
      widget.onError?.call(error);

      // إغلاق بعد 3 ثوان
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          Navigator.of(context).pop();
        }
      });
    }
  }

  /// إلغاء التحميل
  void _cancelDownload() {
    setState(() {
      _isCancelled = true;
      _statusMessage = 'تم إلغاء التحميل';
      _showCancelButton = false;
    });

    _rotationController.stop();
    widget.onCancel?.call();

    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        Navigator.of(context).pop();
      }
    });
  }

  void _updateProgress(double progress, String message) {
    if (mounted) {
      setState(() {
        _progress = progress;
        _statusMessage = message;
      });
      widget.onProgress?.call(progress);
    }
  }

  void _completeDownload() {
    if (mounted) {
      setState(() {
        _progress = 1.0;
        _isCompleted = true;
        _statusMessage = 'تم التحميل بنجاح!';
      });

      _rotationController.stop();
      _scaleController.forward();

      widget.onComplete?.call();

      // إغلاق تلقائي بعد ثانية
      Future.delayed(const Duration(milliseconds: 1000), () {
        if (mounted) {
          Navigator.of(context).pop();
        }
      });
    }
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: themeProvider.isDarkMode ? Colors.grey[850] : Colors.white,
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // أيقونة متحركة
                AnimatedBuilder(
                  animation: _scaleAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _scaleAnimation.value,
                      child: Container(
                        width: 100,
                        height: 100,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            colors:
                                _isCompleted
                                    ? [Colors.green[400]!, Colors.green[600]!]
                                    : [Colors.blue[400]!, Colors.blue[600]!],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: (_isCompleted ? Colors.green : Colors.blue)
                                  .withValues(alpha: 0.3),
                              blurRadius: 20,
                              spreadRadius: 2,
                            ),
                          ],
                        ),
                        child:
                            _isCompleted
                                ? const Icon(
                                  Icons.check_rounded,
                                  color: Colors.white,
                                  size: 50,
                                )
                                : AnimatedBuilder(
                                  animation: _rotationAnimation,
                                  builder: (context, child) {
                                    return Transform.rotate(
                                      angle:
                                          _rotationAnimation.value *
                                          2 *
                                          3.14159,
                                      child: const Icon(
                                        Icons.download_rounded,
                                        color: Colors.white,
                                        size: 50,
                                      ),
                                    );
                                  },
                                ),
                      ),
                    );
                  },
                ),
                const SizedBox(height: 24),

                // عنوان
                Text(
                  _isCompleted ? 'تم التحميل!' : 'جاري التحميل...',
                  style: GoogleFonts.cairo(
                    fontSize: 22,
                    fontWeight: FontWeight.w700,
                    color:
                        themeProvider.isDarkMode
                            ? Colors.white
                            : Colors.grey[800],
                  ),
                ),
                const SizedBox(height: 8),

                // اسم الملف
                Text(
                  widget.pdf.name,
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 24),

                // مؤشر التقدم
                Container(
                  width: double.infinity,
                  height: 8,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(4),
                    child: LinearProgressIndicator(
                      value: _progress,
                      backgroundColor: Colors.transparent,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        _isCompleted ? Colors.green[600]! : Colors.blue[600]!,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 12),

                // نسبة التقدم ومعلومات التحميل
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _statusMessage,
                            style: GoogleFonts.cairo(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                          if (_downloadedSize.isNotEmpty) ...[
                            const SizedBox(height: 2),
                            Text(
                              'تم تحميل: $_downloadedSize',
                              style: GoogleFonts.cairo(
                                fontSize: 10,
                                color: Colors.grey[500],
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                    Text(
                      '${(_progress * 100).toInt()}%',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight: FontWeight.w700,
                        color:
                            _isCompleted
                                ? Colors.green[600]
                                : _isCancelled
                                ? Colors.red[600]
                                : Colors.blue[600],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // رسالة إضافية
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: (_isCompleted ? Colors.green : Colors.blue)[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: (_isCompleted ? Colors.green : Colors.blue)[200]!,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        _isCompleted ? Icons.folder_open : Icons.info_outline,
                        color: (_isCompleted ? Colors.green : Colors.blue)[600],
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _isCompleted
                              ? 'تم حفظ الملف في مجلد التحميلات'
                              : _isCancelled
                              ? 'تم إلغاء التحميل'
                              : 'يتم حفظ الملف في مجلد التحميلات',
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            color:
                                _isCompleted
                                    ? Colors.green[700]
                                    : _isCancelled
                                    ? Colors.red[700]
                                    : Colors.blue[700],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // زر الإلغاء
                if (_showCancelButton && !_isCompleted && !_isCancelled) ...[
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    child: TextButton(
                      onPressed: _cancelDownload,
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                          side: BorderSide(color: Colors.red[300]!),
                        ),
                      ),
                      child: Text(
                        'إلغاء التحميل',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.red[600],
                        ),
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }
}
