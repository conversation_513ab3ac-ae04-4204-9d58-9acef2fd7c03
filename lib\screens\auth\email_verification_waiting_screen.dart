import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../providers/auth_provider.dart';
import '../../providers/theme_provider.dart';
import '../../widgets/glass_morphism.dart';

/// صفحة انتظار التحقق من البريد الإلكتروني
class EmailVerificationWaitingScreen extends StatefulWidget {
  final String email;
  final String password;

  const EmailVerificationWaitingScreen({
    super.key,
    required this.email,
    required this.password,
  });

  @override
  State<EmailVerificationWaitingScreen> createState() =>
      _EmailVerificationWaitingScreenState();
}

class _EmailVerificationWaitingScreenState
    extends State<EmailVerificationWaitingScreen> {
  bool _isChecking = false;

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          body: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors:
                    themeProvider.isDarkMode
                        ? [
                          const Color(0xFF1A1A2E),
                          const Color(0xFF16213E),
                          const Color(0xFF0F3460),
                        ]
                        : [
                          const Color(0xFF667eea),
                          const Color(0xFF764ba2),
                          const Color(0xFF6B73FF),
                        ],
              ),
            ),
            child: SafeArea(
              child: Center(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24),
                  child: _buildVerificationCard(themeProvider),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildVerificationCard(ThemeProvider themeProvider) {
    return GlassMorphism(
      blur: 20,
      opacity: 0.2,
      child: Container(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildIcon(themeProvider),
            const SizedBox(height: 24),
            _buildTitle(themeProvider),
            const SizedBox(height: 16),
            _buildDescription(themeProvider),
            const SizedBox(height: 32),
            _buildEmailInfo(themeProvider),
            const SizedBox(height: 24),
            _buildButtons(themeProvider),
          ],
        ),
      ),
    );
  }

  Widget _buildIcon(ThemeProvider themeProvider) {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          colors: [
            themeProvider.isDarkMode
                ? const Color(0xFF4CAF50)
                : const Color(0xFF2196F3),
            themeProvider.isDarkMode
                ? const Color(0xFF8BC34A)
                : const Color(0xFF21CBF3),
          ],
        ),
      ),
      child: const Icon(Icons.email_outlined, size: 40, color: Colors.white),
    );
  }

  Widget _buildTitle(ThemeProvider themeProvider) {
    return Text(
      'تحقق من بريدك الإلكتروني',
      style: GoogleFonts.cairo(
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: themeProvider.isDarkMode ? Colors.white : Colors.white,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildDescription(ThemeProvider themeProvider) {
    return Text(
      'تم إرسال رابط التفعيل إلى بريدك الإلكتروني.\nيرجى فتح البريد والنقر على الرابط لتفعيل حسابك.',
      style: GoogleFonts.cairo(
        fontSize: 16,
        color:
            themeProvider.isDarkMode
                ? Colors.white.withValues(alpha: 0.8)
                : Colors.white.withValues(alpha: 0.9),
        height: 1.5,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildEmailInfo(ThemeProvider themeProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color:
            themeProvider.isDarkMode
                ? Colors.white.withValues(alpha: 0.1)
                : Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.email,
            color: themeProvider.isDarkMode ? Colors.white70 : Colors.white,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              widget.email,
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: themeProvider.isDarkMode ? Colors.white : Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildButtons(ThemeProvider themeProvider) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return Column(
          children: [
            // زر التحقق من التفعيل
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: _isChecking ? null : _checkVerification,
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      themeProvider.isDarkMode
                          ? const Color(0xFF4CAF50)
                          : const Color(0xFF2196F3),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                  elevation: 8,
                ),
                child:
                    _isChecking
                        ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                        : Text(
                          'تحقق من التفعيل',
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
              ),
            ),
            const SizedBox(height: 16),
            // زر إعادة الإرسال
            TextButton(
              onPressed: authProvider.isLoading ? null : _resendEmail,
              child: Text(
                'إعادة إرسال الإيميل',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color:
                      themeProvider.isDarkMode ? Colors.white70 : Colors.white,
                  decoration: TextDecoration.underline,
                ),
              ),
            ),
            const SizedBox(height: 16),
            // زر العودة لتسجيل الدخول
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'العودة لتسجيل الدخول',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color:
                      themeProvider.isDarkMode ? Colors.white70 : Colors.white,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> _checkVerification() async {
    setState(() {
      _isChecking = true;
    });

    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    // محاولة تسجيل الدخول للتحقق من التفعيل
    final success = await authProvider.signInWithEmail(
      widget.email,
      widget.password,
    );

    if (success && mounted) {
      // إذا نجح تسجيل الدخول، فهذا يعني أن البريد مفعل
      Navigator.of(context).pushNamedAndRemoveUntil('/home', (route) => false);
    } else {
      // إظهار رسالة خطأ إذا لم يتم التفعيل بعد
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              authProvider.error ?? 'لم يتم تفعيل الحساب بعد',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.orange,
          ),
        );
      }
    }

    setState(() {
      _isChecking = false;
    });
  }

  Future<void> _resendEmail() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    final success = await authProvider.resendEmailVerification(
      widget.email,
      widget.password,
    );

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'تم إرسال إيميل التفعيل مرة أخرى',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: Colors.green,
        ),
      );
    }
  }
}
