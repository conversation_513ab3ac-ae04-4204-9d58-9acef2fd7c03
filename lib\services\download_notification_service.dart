import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter/foundation.dart';

/// خدمة الإشعارات للتحميلات الطويلة
class DownloadNotificationService {
  static final FlutterLocalNotificationsPlugin _notifications =
      FlutterLocalNotificationsPlugin();

  static bool _isInitialized = false;
  static int _notificationId = 1000;

  /// تهيئة خدمة الإشعارات
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      const androidSettings = AndroidInitializationSettings(
        '@mipmap/ic_launcher',
      );
      const iosSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      const initSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );

      await _notifications.initialize(initSettings);
      _isInitialized = true;

      if (kDebugMode) {
        print('✅ تم تهيئة خدمة الإشعارات بنجاح');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تهيئة الإشعارات: $e');
      }
    }
  }

  /// إظهار إشعار بدء التحميل
  static Future<int> showDownloadStarted({
    required String fileName,
    required String fileSize,
  }) async {
    await initialize();

    final notificationId = _notificationId++;

    try {
      const androidDetails = AndroidNotificationDetails(
        'download_channel',
        'تحميل الملفات',
        channelDescription: 'إشعارات تحميل ملفات PDF',
        importance: Importance.low,
        priority: Priority.low,
        showProgress: true,
        maxProgress: 100,
        progress: 0,
        ongoing: true,
        autoCancel: false,
        icon: '@drawable/ic_download',
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: false,
        presentBadge: true,
        presentSound: false,
      );

      final notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _notifications.show(
        notificationId,
        'تحميل ملف PDF',
        'بدء تحميل: $fileName ($fileSize)',
        notificationDetails,
      );

      if (kDebugMode) {
        print('📱 تم إظهار إشعار بدء التحميل: $fileName');
      }

      return notificationId;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إظهار إشعار التحميل: $e');
      }
      return -1;
    }
  }

  /// تحديث تقدم التحميل
  static Future<void> updateDownloadProgress({
    required int notificationId,
    required String fileName,
    required int progress,
    required String downloadSpeed,
    required String downloadedSize,
    required String totalSize,
  }) async {
    if (!_isInitialized || notificationId == -1) return;

    try {
      final androidDetails = AndroidNotificationDetails(
        'download_channel',
        'تحميل الملفات',
        channelDescription: 'إشعارات تحميل ملفات PDF',
        importance: Importance.low,
        priority: Priority.low,
        showProgress: true,
        maxProgress: 100,
        progress: progress,
        ongoing: true,
        autoCancel: false,
        icon: '@drawable/ic_download',
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: false,
        presentBadge: true,
        presentSound: false,
      );

      final notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _notifications.show(
        notificationId,
        'تحميل ملف PDF ($progress%)',
        '$fileName\n$downloadedSize من $totalSize • $downloadSpeed',
        notificationDetails,
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحديث تقدم التحميل: $e');
      }
    }
  }

  /// إظهار إشعار اكتمال التحميل
  static Future<void> showDownloadCompleted({
    required int notificationId,
    required String fileName,
    required String filePath,
  }) async {
    if (!_isInitialized || notificationId == -1) return;

    try {
      const androidDetails = AndroidNotificationDetails(
        'download_complete_channel',
        'اكتمال التحميل',
        channelDescription: 'إشعارات اكتمال تحميل الملفات',
        importance: Importance.high,
        priority: Priority.high,
        icon: '@drawable/ic_download_done',
        color: Colors.green,
        autoCancel: true,
        ongoing: false,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      final notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _notifications.show(
        notificationId,
        '✅ تم التحميل بنجاح',
        'تم تحميل: $fileName\nالموقع: مجلد التحميلات',
        notificationDetails,
      );

      if (kDebugMode) {
        print('✅ تم إظهار إشعار اكتمال التحميل: $fileName');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إظهار إشعار الاكتمال: $e');
      }
    }
  }

  /// إظهار إشعار فشل التحميل
  static Future<void> showDownloadFailed({
    required int notificationId,
    required String fileName,
    required String error,
  }) async {
    if (!_isInitialized || notificationId == -1) return;

    try {
      const androidDetails = AndroidNotificationDetails(
        'download_error_channel',
        'أخطاء التحميل',
        channelDescription: 'إشعارات أخطاء تحميل الملفات',
        importance: Importance.high,
        priority: Priority.high,
        icon: '@drawable/ic_error',
        color: Colors.red,
        autoCancel: true,
        ongoing: false,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      final notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _notifications.show(
        notificationId,
        '❌ فشل التحميل',
        'فشل تحميل: $fileName\nالسبب: $error',
        notificationDetails,
      );

      if (kDebugMode) {
        print('❌ تم إظهار إشعار فشل التحميل: $fileName');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إظهار إشعار الفشل: $e');
      }
    }
  }

  /// إظهار إشعار إلغاء التحميل
  static Future<void> showDownloadCancelled({
    required int notificationId,
    required String fileName,
  }) async {
    if (!_isInitialized || notificationId == -1) return;

    try {
      const androidDetails = AndroidNotificationDetails(
        'download_cancelled_channel',
        'إلغاء التحميل',
        channelDescription: 'إشعارات إلغاء تحميل الملفات',
        importance: Importance.low,
        priority: Priority.low,
        icon: '@drawable/ic_cancel',
        color: Colors.orange,
        autoCancel: true,
        ongoing: false,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: false,
        presentBadge: true,
        presentSound: false,
      );

      final notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _notifications.show(
        notificationId,
        '⏹️ تم إلغاء التحميل',
        'تم إلغاء تحميل: $fileName',
        notificationDetails,
      );

      if (kDebugMode) {
        print('⏹️ تم إظهار إشعار إلغاء التحميل: $fileName');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إظهار إشعار الإلغاء: $e');
      }
    }
  }

  /// إلغاء إشعار معين
  static Future<void> cancelNotification(int notificationId) async {
    if (!_isInitialized || notificationId == -1) return;

    try {
      await _notifications.cancel(notificationId);
      if (kDebugMode) {
        print('🗑️ تم إلغاء الإشعار: $notificationId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إلغاء الإشعار: $e');
      }
    }
  }

  /// إلغاء جميع الإشعارات
  static Future<void> cancelAllNotifications() async {
    if (!_isInitialized) return;

    try {
      await _notifications.cancelAll();
      if (kDebugMode) {
        print('🗑️ تم إلغاء جميع الإشعارات');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إلغاء جميع الإشعارات: $e');
      }
    }
  }
}
