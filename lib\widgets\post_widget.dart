import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../models/post_model.dart';
import '../providers/theme_provider.dart';
import '../providers/auth_provider.dart' as app_auth;
import '../services/post_service.dart';

import 'comment_widget.dart';
import 'poll_widget.dart';

class PostWidget extends StatefulWidget {
  final PostModel post;
  final VoidCallback? onPostUpdated;

  const PostWidget({super.key, required this.post, this.onPostUpdated});

  @override
  State<PostWidget> createState() => _PostWidgetState();
}

class _PostWidgetState extends State<PostWidget> with TickerProviderStateMixin {
  late AnimationController _likeAnimationController;
  late Animation<double> _likeAnimation;

  bool _isLiked = false;
  bool _showComments = false;
  bool _isLoading = false;

  final TextEditingController _commentController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _checkIfLiked();
    _incrementViews();
  }

  void _initializeAnimations() {
    _likeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _likeAnimation = Tween<double>(begin: 1.0, end: 1.3).animate(
      CurvedAnimation(
        parent: _likeAnimationController,
        curve: Curves.elasticOut,
      ),
    );
  }

  void _checkIfLiked() {
    try {
      // التحقق من إعجاب المستخدم الحالي بالمنشور
      final authProvider = Provider.of<app_auth.AuthProvider>(
        context,
        listen: false,
      );
      final currentUser = authProvider.firebaseUser;

      if (currentUser != null) {
        setState(() {
          _isLiked = widget.post.likedBy.contains(currentUser.uid);
        });
      }
    } catch (e) {
      debugPrint('خطأ في التحقق من الإعجاب: $e');
    }
  }

  void _incrementViews() {
    PostService.incrementViews(widget.post.id);
  }

  @override
  void dispose() {
    _likeAnimationController.dispose();
    _commentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color:
                themeProvider.isDarkMode
                    ? const Color(0xFF1E293B)
                    : Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildPostHeader(themeProvider),
              _buildPostContent(themeProvider),
              if (widget.post.imageUrls.isNotEmpty)
                _buildImageGallery(themeProvider),
              if (widget.post.pollData != null) _buildPoll(themeProvider),
              if (widget.post.attachments.isNotEmpty)
                _buildAttachments(themeProvider),
              _buildPostStats(themeProvider),
              _buildActionButtons(themeProvider),
              if (_showComments) _buildCommentsSection(themeProvider),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPostHeader(ThemeProvider themeProvider) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundColor: const Color(0xFF10B981),
            child: Text(
              widget.post.isAnonymous ? '؟' : widget.post.authorName[0],
              style: GoogleFonts.cairo(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      widget.post.authorName,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color:
                            themeProvider.isDarkMode
                                ? Colors.white
                                : Colors.black87,
                      ),
                    ),
                    if (widget.post.isAnonymous) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.orange.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'مجهول',
                          style: GoogleFonts.cairo(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                            color: Colors.orange,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    Text(
                      _formatTime(widget.post.createdAt),
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color:
                            themeProvider.isDarkMode
                                ? Colors.grey[400]
                                : Colors.grey[600],
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: _getCategoryColor(
                          widget.post.category,
                        ).withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        widget.post.category,
                        style: GoogleFonts.cairo(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                          color: _getCategoryColor(widget.post.category),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          PopupMenuButton<String>(
            icon: Icon(
              Icons.more_vert,
              color:
                  themeProvider.isDarkMode
                      ? Colors.grey[400]
                      : Colors.grey[600],
            ),
            onSelected: _handleMenuAction,
            itemBuilder:
                (context) => [
                  PopupMenuItem(
                    value: 'share',
                    child: Row(
                      children: [
                        const Icon(Icons.share, size: 20),
                        const SizedBox(width: 12),
                        Text('مشاركة', style: GoogleFonts.cairo()),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'report',
                    child: Row(
                      children: [
                        const Icon(Icons.flag, size: 20, color: Colors.red),
                        const SizedBox(width: 12),
                        Text(
                          'إبلاغ',
                          style: GoogleFonts.cairo(color: Colors.red),
                        ),
                      ],
                    ),
                  ),
                ],
          ),
        ],
      ),
    );
  }

  Widget _buildPostContent(ThemeProvider themeProvider) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Text(
        widget.post.content,
        style: GoogleFonts.cairo(
          fontSize: 15,
          height: 1.6,
          color: themeProvider.isDarkMode ? Colors.white : Colors.black87,
        ),
        textDirection: TextDirection.rtl,
      ),
    );
  }

  Widget _buildImageGallery(ThemeProvider themeProvider) {
    if (widget.post.imageUrls.length == 1) {
      return Container(
        margin: const EdgeInsets.all(16),
        height: 200,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          image: DecorationImage(
            image: NetworkImage(widget.post.imageUrls.first),
            fit: BoxFit.cover,
          ),
        ),
      );
    }

    return Container(
      height: 150,
      margin: const EdgeInsets.all(16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: widget.post.imageUrls.length,
        itemBuilder: (context, index) {
          return Container(
            width: 150,
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              image: DecorationImage(
                image: NetworkImage(widget.post.imageUrls[index]),
                fit: BoxFit.cover,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildPoll(ThemeProvider themeProvider) {
    return Container(
      margin: const EdgeInsets.all(16),
      child: PollWidget(
        pollData: widget.post.pollData!,
        postId: widget.post.id,
        themeProvider: themeProvider,
      ),
    );
  }

  Widget _buildAttachments(ThemeProvider themeProvider) {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        children:
            widget.post.attachments.map((attachment) {
              final fileName = attachment.split('/').last;
              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color:
                      themeProvider.isDarkMode
                          ? const Color(0xFF0F172A)
                          : Colors.grey[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color:
                        themeProvider.isDarkMode
                            ? Colors.grey[700]!
                            : Colors.grey[200]!,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      _getFileIcon(fileName),
                      color: const Color(0xFF10B981),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        fileName,
                        style: GoogleFonts.cairo(
                          color:
                              themeProvider.isDarkMode
                                  ? Colors.white
                                  : Colors.black87,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => _downloadFile(attachment),
                      icon: const Icon(
                        Icons.download,
                        color: Color(0xFF10B981),
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
      ),
    );
  }

  Widget _buildPostStats(ThemeProvider themeProvider) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          if (widget.post.likes > 0) ...[
            Icon(Icons.favorite, size: 16, color: Colors.red[400]),
            const SizedBox(width: 4),
            Text(
              '${widget.post.likes}',
              style: GoogleFonts.cairo(
                fontSize: 12,
                color:
                    themeProvider.isDarkMode
                        ? Colors.grey[400]
                        : Colors.grey[600],
              ),
            ),
            const SizedBox(width: 16),
          ],
          if (widget.post.comments.isNotEmpty) ...[
            Icon(
              Icons.comment,
              size: 16,
              color:
                  themeProvider.isDarkMode
                      ? Colors.grey[400]
                      : Colors.grey[600],
            ),
            const SizedBox(width: 4),
            Text(
              '${widget.post.comments.length}',
              style: GoogleFonts.cairo(
                fontSize: 12,
                color:
                    themeProvider.isDarkMode
                        ? Colors.grey[400]
                        : Colors.grey[600],
              ),
            ),
            const SizedBox(width: 16),
          ],
          if (widget.post.shares > 0) ...[
            Icon(
              Icons.share,
              size: 16,
              color:
                  themeProvider.isDarkMode
                      ? Colors.grey[400]
                      : Colors.grey[600],
            ),
            const SizedBox(width: 4),
            Text(
              '${widget.post.shares}',
              style: GoogleFonts.cairo(
                fontSize: 12,
                color:
                    themeProvider.isDarkMode
                        ? Colors.grey[400]
                        : Colors.grey[600],
              ),
            ),
            const SizedBox(width: 16),
          ],
          const Spacer(),
          if (widget.post.views > 0)
            Text(
              '${widget.post.views} مشاهدة',
              style: GoogleFonts.cairo(
                fontSize: 12,
                color:
                    themeProvider.isDarkMode
                        ? Colors.grey[400]
                        : Colors.grey[600],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(ThemeProvider themeProvider) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color:
                themeProvider.isDarkMode
                    ? Colors.grey[700]!
                    : Colors.grey[200]!,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildActionButton(
              icon: _isLiked ? Icons.favorite : Icons.favorite_border,
              label: 'إعجاب',
              color: _isLiked ? Colors.red : null,
              onTap: _toggleLike,
              themeProvider: themeProvider,
            ),
          ),
          Expanded(
            child: _buildActionButton(
              icon: Icons.comment_outlined,
              label: 'تعليق',
              onTap: _toggleComments,
              themeProvider: themeProvider,
            ),
          ),
          Expanded(
            child: _buildActionButton(
              icon: Icons.share_outlined,
              label: 'مشاركة',
              onTap: _sharePost,
              themeProvider: themeProvider,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required ThemeProvider themeProvider,
    Color? color,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedBuilder(
              animation: _likeAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale:
                      icon == Icons.favorite && _isLiked
                          ? _likeAnimation.value
                          : 1.0,
                  child: Icon(
                    icon,
                    size: 20,
                    color:
                        color ??
                        (themeProvider.isDarkMode
                            ? Colors.grey[400]
                            : Colors.grey[600]),
                  ),
                );
              },
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color:
                    color ??
                    (themeProvider.isDarkMode
                        ? Colors.grey[400]
                        : Colors.grey[600]),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCommentsSection(ThemeProvider themeProvider) {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color:
                themeProvider.isDarkMode
                    ? Colors.grey[700]!
                    : Colors.grey[200]!,
            width: 0.5,
          ),
        ),
      ),
      child: Column(
        children: [
          // Comment input
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 16,
                  backgroundColor: const Color(0xFF10B981),
                  child: Text(
                    'أ',
                    style: GoogleFonts.cairo(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextField(
                    controller: _commentController,
                    textDirection: TextDirection.rtl,
                    style: GoogleFonts.cairo(
                      color:
                          themeProvider.isDarkMode
                              ? Colors.white
                              : Colors.black87,
                    ),
                    decoration: InputDecoration(
                      hintText: 'اكتب تعليقاً...',
                      hintStyle: GoogleFonts.cairo(
                        color:
                            themeProvider.isDarkMode
                                ? Colors.grey[400]
                                : Colors.grey[500],
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20),
                        borderSide: BorderSide(
                          color:
                              themeProvider.isDarkMode
                                  ? Colors.grey[600]!
                                  : Colors.grey[300]!,
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20),
                        borderSide: BorderSide(
                          color:
                              themeProvider.isDarkMode
                                  ? Colors.grey[600]!
                                  : Colors.grey[300]!,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20),
                        borderSide: const BorderSide(
                          color: Color(0xFF10B981),
                          width: 2,
                        ),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      suffixIcon: IconButton(
                        onPressed: _isLoading ? null : _addComment,
                        icon:
                            _isLoading
                                ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      Color(0xFF10B981),
                                    ),
                                  ),
                                )
                                : const Icon(
                                  Icons.send,
                                  color: Color(0xFF10B981),
                                ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Comments list
          ...widget.post.comments.map(
            (comment) => CommentWidget(
              comment: comment,
              postId: widget.post.id,
              themeProvider: themeProvider,
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  String _formatTime(DateTime? dateTime) {
    if (dateTime == null) return 'الآن';

    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inDays < 1) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'عام':
        return const Color(0xFF6B7280);
      case 'سؤال':
        return const Color(0xFF3B82F6);
      case 'إعلان':
        return const Color(0xFFEF4444);
      case 'دراسة':
        return const Color(0xFF10B981);
      case 'نقاش':
        return const Color(0xFF8B5CF6);
      case 'مصدر':
        return const Color(0xFFF59E0B);
      default:
        return const Color(0xFF6B7280);
    }
  }

  IconData _getFileIcon(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    switch (extension) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow;
      default:
        return Icons.insert_drive_file;
    }
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'share':
        _sharePost();
        break;
      case 'report':
        _reportPost();
        break;
    }
  }

  Future<void> _toggleLike() async {
    setState(() {
      _isLiked = !_isLiked;
    });

    if (_isLiked) {
      _likeAnimationController.forward().then((_) {
        _likeAnimationController.reverse();
      });
      HapticFeedback.lightImpact();
    }

    final success = await PostService.toggleLike(widget.post.id);
    if (!success) {
      setState(() {
        _isLiked = !_isLiked;
      });
    }

    widget.onPostUpdated?.call();
  }

  void _toggleComments() {
    setState(() {
      _showComments = !_showComments;
    });
  }

  Future<void> _addComment() async {
    final comment = _commentController.text.trim();
    if (comment.isEmpty) return;

    setState(() {
      _isLoading = true;
    });

    final success = await PostService.addComment(
      postId: widget.post.id,
      comment: comment,
    );

    if (success) {
      _commentController.clear();
      widget.onPostUpdated?.call();
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _sharePost() async {
    final success = await PostService.sharePost(widget.post.id);
    if (success) {
      widget.onPostUpdated?.call();
      _showSnackBar('تم مشاركة المنشور بنجاح');
    }
  }

  void _reportPost() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('إبلاغ عن المنشور', style: GoogleFonts.cairo()),
            content: Text(
              'هل أنت متأكد من أنك تريد الإبلاغ عن هذا المنشور؟',
              style: GoogleFonts.cairo(),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('إلغاء', style: GoogleFonts.cairo()),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  _showSnackBar('تم إرسال البلاغ بنجاح');
                },
                child: Text(
                  'إبلاغ',
                  style: GoogleFonts.cairo(color: Colors.red),
                ),
              ),
            ],
          ),
    );
  }

  void _downloadFile(String url) async {
    try {
      // استخراج اسم الملف من الرابط
      String fileName = url.split('/').last;
      if (!fileName.contains('.')) {
        fileName = 'file_${DateTime.now().millisecondsSinceEpoch}.pdf';
      }

      // ميزة التحميل مُعطلة مؤقتاً
      _showSnackBar('ميزة التحميل غير متاحة حالياً');
    } catch (e) {
      _showSnackBar('خطأ في تحميل الملف: $e');
    }
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: GoogleFonts.cairo()),
        backgroundColor: const Color(0xFF10B981),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }
}
