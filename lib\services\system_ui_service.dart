import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// خدمة إدارة واجهة النظام وشريط الحالة
class SystemUIService {
  static bool _isInitialized = false;

  /// تهيئة إعدادات واجهة النظام
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // إعداد شريط الحالة للوضع الفاتح
      await setLightStatusBar();

      // إعداد شريط التنقل
      await setNavigationBarStyle();

      _isInitialized = true;
      debugPrint('✅ تم تهيئة خدمة واجهة النظام');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة واجهة النظام: $e');
    }
  }

  /// إعداد شريط الحالة للوضع الفاتح
  static Future<void> setLightStatusBar() async {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness:
            Brightness.dark, // أيقونات داكنة على خلفية فاتحة
        statusBarBrightness: Brightness.light, // للـ iOS
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
        systemNavigationBarDividerColor: Colors.transparent,
      ),
    );

    // إجبار التحديث
    await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    debugPrint('🌞 تم تطبيق شريط الحالة الفاتح');
  }

  /// إعداد شريط الحالة للوضع المظلم
  static Future<void> setDarkStatusBar() async {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness:
            Brightness.light, // أيقونات فاتحة على خلفية داكنة
        statusBarBrightness: Brightness.dark, // للـ iOS
        systemNavigationBarColor: Color(0xFF1A1A1A),
        systemNavigationBarIconBrightness: Brightness.light,
        systemNavigationBarDividerColor: Colors.transparent,
      ),
    );

    // إجبار التحديث
    await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    debugPrint('🌙 تم تطبيق شريط الحالة المظلم');
  }

  /// إعداد شريط الحالة حسب السمة
  static Future<void> setStatusBarForTheme(bool isDarkMode) async {
    try {
      if (isDarkMode) {
        await setDarkStatusBar();
      } else {
        await setLightStatusBar();
      }

      // تأخير قصير للتأكد من التطبيق
      await Future.delayed(const Duration(milliseconds: 100));
      debugPrint(
        '🎨 تم تحديث شريط الحالة للثيم: ${isDarkMode ? "مظلم" : "فاتح"}',
      );
    } catch (e) {
      debugPrint('❌ خطأ في تحديث شريط الحالة: $e');
    }
  }

  /// إعداد شريط التنقل
  static Future<void> setNavigationBarStyle() async {
    await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
  }

  /// إعداد شريط الحالة لصفحة معينة
  static Future<void> setStatusBarForPage({
    Color? statusBarColor,
    Brightness? statusBarIconBrightness,
    Color? navigationBarColor,
    Brightness? navigationBarIconBrightness,
  }) async {
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: statusBarColor ?? Colors.transparent,
        statusBarIconBrightness: statusBarIconBrightness ?? Brightness.dark,
        statusBarBrightness:
            statusBarIconBrightness == Brightness.dark
                ? Brightness.light
                : Brightness.dark,
        systemNavigationBarColor: navigationBarColor ?? Colors.transparent,
        systemNavigationBarIconBrightness:
            navigationBarIconBrightness ?? Brightness.dark,
        systemNavigationBarDividerColor: Colors.transparent,
      ),
    );
  }

  /// إخفاء شريط الحالة وشريط التنقل (للوضع الملء الشاشة)
  static Future<void> hideSystemUI() async {
    await SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
  }

  /// إظهار شريط الحالة وشريط التنقل
  static Future<void> showSystemUI() async {
    await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
  }

  /// إعداد اتجاه الشاشة
  static Future<void> setOrientation(
    List<DeviceOrientation> orientations,
  ) async {
    await SystemChrome.setPreferredOrientations(orientations);
  }

  /// قفل الشاشة في الوضع العمودي
  static Future<void> lockPortrait() async {
    await setOrientation([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }

  /// قفل الشاشة في الوضع الأفقي
  static Future<void> lockLandscape() async {
    await setOrientation([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  /// السماح بجميع الاتجاهات
  static Future<void> allowAllOrientations() async {
    await setOrientation([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  /// الحصول على ارتفاع شريط الحالة
  static double getStatusBarHeight(BuildContext context) {
    return MediaQuery.of(context).viewPadding.top;
  }

  /// الحصول على ارتفاع شريط التنقل
  static double getNavigationBarHeight(BuildContext context) {
    return MediaQuery.of(context).viewPadding.bottom;
  }

  /// التحقق من وجود notch أو dynamic island
  static bool hasNotch(BuildContext context) {
    return MediaQuery.of(context).viewPadding.top > 24;
  }

  /// الحصول على SafeArea padding
  static EdgeInsets getSafeAreaPadding(BuildContext context) {
    return MediaQuery.of(context).viewPadding;
  }
}

/// Widget مخصص للتعامل مع SafeArea بذكاء
class SmartSafeArea extends StatelessWidget {
  final Widget child;
  final bool top;
  final bool bottom;
  final bool left;
  final bool right;
  final Color? backgroundColor;

  const SmartSafeArea({
    super.key,
    required this.child,
    this.top = true,
    this.bottom = true,
    this.left = true,
    this.right = true,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: backgroundColor,
      child: SafeArea(
        top: top,
        bottom: bottom,
        left: left,
        right: right,
        child: child,
      ),
    );
  }
}

/// Widget لإدارة شريط الحالة لصفحة معينة
class StatusBarManager extends StatefulWidget {
  final Widget child;
  final Color? statusBarColor;
  final Brightness? statusBarIconBrightness;
  final Color? navigationBarColor;
  final Brightness? navigationBarIconBrightness;
  final bool restoreOnDispose;

  const StatusBarManager({
    super.key,
    required this.child,
    this.statusBarColor,
    this.statusBarIconBrightness,
    this.navigationBarColor,
    this.navigationBarIconBrightness,
    this.restoreOnDispose = true,
  });

  @override
  State<StatusBarManager> createState() => _StatusBarManagerState();
}

class _StatusBarManagerState extends State<StatusBarManager> {
  SystemUiOverlayStyle? _previousStyle;

  @override
  void initState() {
    super.initState();

    // تطبيق الإعدادات الجديدة فوراً
    _applyStatusBarStyle();
  }

  @override
  void didUpdateWidget(StatusBarManager oldWidget) {
    super.didUpdateWidget(oldWidget);

    // إعادة تطبيق الإعدادات عند تغيير الخصائص
    if (oldWidget.statusBarIconBrightness != widget.statusBarIconBrightness ||
        oldWidget.statusBarColor != widget.statusBarColor) {
      _applyStatusBarStyle();
    }
  }

  void _applyStatusBarStyle() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        SystemChrome.setSystemUIOverlayStyle(
          SystemUiOverlayStyle(
            statusBarColor: widget.statusBarColor ?? Colors.transparent,
            statusBarIconBrightness:
                widget.statusBarIconBrightness ?? Brightness.dark,
            statusBarBrightness:
                widget.statusBarIconBrightness == Brightness.dark
                    ? Brightness.light
                    : Brightness.dark,
            systemNavigationBarColor:
                widget.navigationBarColor ?? Colors.transparent,
            systemNavigationBarIconBrightness:
                widget.navigationBarIconBrightness ?? Brightness.dark,
            systemNavigationBarDividerColor: Colors.transparent,
          ),
        );
      }
    });
  }

  @override
  void dispose() {
    // استعادة الإعدادات السابقة
    if (widget.restoreOnDispose && _previousStyle != null) {
      SystemChrome.setSystemUIOverlayStyle(_previousStyle!);
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

/// Mixin لإدارة شريط الحالة في الصفحات
mixin StatusBarMixin<T extends StatefulWidget> on State<T> {
  /// إعداد شريط الحالة عند بناء الصفحة
  void setupStatusBar({
    Color? statusBarColor,
    Brightness? statusBarIconBrightness,
    Color? navigationBarColor,
    Brightness? navigationBarIconBrightness,
  }) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      SystemUIService.setStatusBarForPage(
        statusBarColor: statusBarColor,
        statusBarIconBrightness: statusBarIconBrightness,
        navigationBarColor: navigationBarColor,
        navigationBarIconBrightness: navigationBarIconBrightness,
      );
    });
  }

  /// إعداد شريط الحالة للوضع الفاتح
  void setupLightStatusBar() {
    setupStatusBar(
      statusBarIconBrightness: Brightness.dark,
      navigationBarIconBrightness: Brightness.dark,
    );
  }

  /// إعداد شريط الحالة للوضع المظلم
  void setupDarkStatusBar() {
    setupStatusBar(
      statusBarIconBrightness: Brightness.light,
      navigationBarIconBrightness: Brightness.light,
    );
  }
}
