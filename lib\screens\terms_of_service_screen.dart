import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class TermsOfServiceScreen extends StatelessWidget {
  const TermsOfServiceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDarkMode ? const Color(0xFF1A1A1A) : Colors.grey[50],
      appBar: AppBar(
        title: Text(
          'شروط الاستخدام',
          style: GoogleFonts.cairo(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: isDarkMode ? Colors.white : const Color(0xFF2C3E50),
          ),
        ),
        backgroundColor: isDarkMode ? const Color(0xFF2D2D2D) : Colors.white,
        elevation: 0,
        iconTheme: IconThemeData(
          color: isDarkMode ? Colors.white : const Color(0xFF2C3E50),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [const Color(0xFF059669), const Color(0xFF10B981)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(15),
                boxShadow: [
                  BoxShadow(
                    color: Colors.green.withValues(alpha: 0.3),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.description_rounded,
                    size: 50,
                    color: Colors.white,
                  ),
                  const SizedBox(height: 10),
                  Text(
                    'شروط الاستخدام',
                    style: GoogleFonts.cairo(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 5),
                  Text(
                    'تطبيق منصة الشريعة و القانون',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      color: Colors.white.withValues(alpha: 0.9),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 25),

            // Last Updated
            Container(
              padding: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                color: isDarkMode ? const Color(0xFF2D2D2D) : Colors.green[50],
                borderRadius: BorderRadius.circular(10),
                border: Border.all(
                  color: isDarkMode ? Colors.green[700]! : Colors.green[200]!,
                ),
              ),
              child: Row(
                children: [
                  Icon(Icons.update, color: Colors.green[600], size: 20),
                  const SizedBox(width: 10),
                  Text(
                    'آخر تحديث: ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: isDarkMode ? Colors.green[300] : Colors.green[700],
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 25),

            // Content Sections
            _buildSection(
              context,
              '1. قبول الشروط',
              'باستخدام تطبيق منصة الشريعة و القانون، فإنك توافق على الالتزام بهذه الشروط والأحكام. إذا كنت لا توافق على أي من هذه الشروط، يرجى عدم استخدام التطبيق.',
              Icons.check_circle_outline,
            ),

            _buildSection(
              context,
              '2. وصف الخدمة',
              '''تطبيق منصة الشريعة و القانون هو منصة تعليمية مخصصة لطلاب كلية الشريعة والقانون تتيح:
• الوصول للمواد الدراسية والملفات
• التفاعل مع المجتمع الطلابي
• مشاركة المحتوى التعليمي
• التواصل مع الزملاء والأساتذة
• متابعة الأخبار والإعلانات الأكاديمية''',
              Icons.school,
            ),

            _buildSection(
              context,
              '3. التسجيل والحساب',
              '''• يجب أن تكون طالباً فيكلية الشريعة والقانون لاستخدام التطبيق
• يجب تقديم معلومات صحيحة ومحدثة عند التسجيل
• أنت مسؤول عن الحفاظ على سرية كلمة المرور
• يجب إخطارنا فوراً بأي استخدام غير مصرح به لحسابك
• حساب واحد فقط لكل مستخدم''',
              Icons.person_add,
            ),

            _buildSection(context, '4. قواعد السلوك', '''يُمنع منعاً باتاً:
• نشر محتوى مسيء أو غير لائق
• التنمر أو المضايقة
• انتهاك حقوق الطبع والنشر
• نشر معلومات كاذبة أو مضللة
• استخدام التطبيق لأغراض تجارية غير مصرح بها
• محاولة اختراق أو إتلاف النظام''', Icons.rule),

            _buildSection(
              context,
              '5. المحتوى والملكية الفكرية',
              '''• المحتوى التعليمي محمي بحقوق الطبع والنشر
• يمكنك استخدام المحتوى للأغراض التعليمية فقط
• لا يجوز إعادة توزيع أو بيع المحتوى
• نحتفظ بحقوق الملكية الفكرية للتطبيق
• المحتوى المُنشأ من المستخدمين يخضع لمراجعتنا''',
              Icons.copyright,
            ),

            _buildSection(
              context,
              '6. الخصوصية وحماية البيانات',
              '''• نلتزم بحماية خصوصيتك وفقاً لسياسة الخصوصية
• نجمع المعلومات الضرورية لتشغيل الخدمة فقط
• لا نشارك معلوماتك مع أطراف ثالثة بدون موافقتك
• يمكنك طلب حذف حسابك ومعلوماتك في أي وقت''',
              Icons.privacy_tip,
            ),

            _buildSection(
              context,
              '7. المسؤوليات والضمانات',
              '''• التطبيق متاح "كما هو" بدون ضمانات صريحة
• نبذل قصارى جهدنا لضمان دقة المحتوى
• لسنا مسؤولين عن أي أضرار ناتجة عن استخدام التطبيق
• أنت مسؤول عن استخدامك للتطبيق والمحتوى
• نحتفظ بالحق في تعديل أو إيقاف الخدمة''',
              Icons.warning,
            ),

            _buildSection(
              context,
              '8. الإنهاء والإيقاف',
              '''يمكننا إنهاء حسابك في الحالات التالية:
• انتهاك شروط الاستخدام
• سوء استخدام التطبيق
• نشاط مشبوه أو ضار
• عدم النشاط لفترة طويلة
• طلبك لحذف الحساب''',
              Icons.block,
            ),

            _buildSection(
              context,
              '9. التحديثات والتغييرات',
              '''• نحتفظ بالحق في تحديث التطبيق وشروط الاستخدام
• سنخطرك بالتغييرات المهمة مسبقاً
• استمرار استخدامك يعني موافقتك على التغييرات
• يمكنك التوقف عن الاستخدام إذا لم توافق على التغييرات''',
              Icons.system_update,
            ),

            _buildSection(
              context,
              '10. القانون المطبق',
              '''• تخضع هذه الشروط للقانون المصري
• أي نزاع يُحل وفقاً للقوانين المصرية
• المحاكم المصرية لها الاختصاص الحصري
• نسعى لحل النزاعات ودياً قبل اللجوء للقضاء''',
              Icons.gavel,
            ),

            _buildSection(
              context,
              '11. التواصل والدعم',
              '''للاستفسارات أو المساعدة:
📧 البريد الإلكتروني: <EMAIL>
📱 التطبيق: استخدم خاصية "اتصل بنا"
🏢 العنوان:كلية الشريعة والقانون - جامعة أسيوط
⏰ أوقات الدعم: 9 صباحاً - 6 مساءً (الأحد - الخميس)''',
              Icons.support_agent,
            ),

            const SizedBox(height: 30),

            // Acceptance Notice
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.orange[400]!, Colors.orange[600]!],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(15),
                boxShadow: [
                  BoxShadow(
                    color: Colors.orange.withValues(alpha: 0.3),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Icon(Icons.handshake, size: 40, color: Colors.white),
                  const SizedBox(height: 10),
                  Text(
                    'موافقتك على الشروط',
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    'باستخدام تطبيق منصة الشريعة و القانون، فإنك توافق على جميع الشروط والأحكام المذكورة أعلاه',
                    textAlign: TextAlign.center,
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.white.withValues(alpha: 0.9),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // Footer
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: isDarkMode ? const Color(0xFF2D2D2D) : Colors.grey[100],
                borderRadius: BorderRadius.circular(15),
                border: Border.all(
                  color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
                ),
              ),
              child: Column(
                children: [
                  Icon(Icons.verified, size: 40, color: Colors.green[600]),
                  const SizedBox(height: 10),
                  Text(
                    'شكراً لاختيارك منصة الشريعة و القانون',
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color:
                          isDarkMode ? Colors.white : const Color(0xFF2C3E50),
                    ),
                  ),
                  const SizedBox(height: 5),
                  Text(
                    'منصة تعليمية آمنة وموثوقة',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(
    BuildContext context,
    String title,
    String content,
    IconData icon,
  ) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF2D2D2D) : Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color:
                isDarkMode
                    ? Colors.black26
                    : Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: Colors.green[700], size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : const Color(0xFF2C3E50),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 15),
          Text(
            content,
            style: GoogleFonts.cairo(
              fontSize: 15,
              height: 1.6,
              color: isDarkMode ? Colors.grey[300] : Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }
}
