import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:io';

class PermissionRequestWidget extends StatefulWidget {
  final VoidCallback? onPermissionGranted;
  final String title;
  final String description;
  final List<Permission> permissions;

  const PermissionRequestWidget({
    super.key,
    this.onPermissionGranted,
    this.title = 'أذونات مطلوبة',
    this.description = 'يحتاج التطبيق إلى أذونات للوصول للملفات',
    this.permissions = const [Permission.storage, Permission.manageExternalStorage],
  });

  @override
  State<PermissionRequestWidget> createState() => _PermissionRequestWidgetState();
}

class _PermissionRequestWidgetState extends State<PermissionRequestWidget> {
  bool _isRequesting = false;
  Map<Permission, PermissionStatus> _permissionStatuses = {};

  @override
  void initState() {
    super.initState();
    _checkPermissions();
  }

  Future<void> _checkPermissions() async {
    Map<Permission, PermissionStatus> statuses = {};
    for (Permission permission in widget.permissions) {
      statuses[permission] = await permission.status;
    }
    
    if (mounted) {
      setState(() {
        _permissionStatuses = statuses;
      });
    }
  }

  Future<void> _requestPermissions() async {
    setState(() {
      _isRequesting = true;
    });

    try {
      if (Platform.isAndroid) {
        // للأندرويد 11+ - طلب إذن إدارة جميع الملفات
        if (widget.permissions.contains(Permission.manageExternalStorage)) {
          var status = await Permission.manageExternalStorage.request();
          if (status.isGranted) {
            widget.onPermissionGranted?.call();
            return;
          }
        }

        // طلب الأذونات العادية
        Map<Permission, PermissionStatus> statuses = await widget.permissions.request();
        
        bool hasAnyPermission = statuses.values.any((status) => status.isGranted);
        
        if (hasAnyPermission) {
          widget.onPermissionGranted?.call();
        } else {
          _showPermissionDeniedDialog();
        }
      }
    } catch (e) {
      debugPrint('خطأ في طلب الأذونات: $e');
      _showErrorDialog();
    } finally {
      if (mounted) {
        setState(() {
          _isRequesting = false;
        });
        _checkPermissions();
      }
    }
  }

  void _showPermissionDeniedDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('أذونات مطلوبة'),
        content: const Text(
          'لم يتم منح الأذونات المطلوبة. يمكنك منحها من إعدادات التطبيق.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              openAppSettings();
            },
            child: const Text('فتح الإعدادات'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خطأ'),
        content: const Text('حدث خطأ أثناء طلب الأذونات. حاول مرة أخرى.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  bool get _hasAllPermissions {
    return _permissionStatuses.values.any((status) => status.isGranted);
  }

  @override
  Widget build(BuildContext context) {
    if (_hasAllPermissions) {
      return const SizedBox.shrink();
    }

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.folder_open,
              size: 48,
              color: Theme.of(context).primaryColor,
            ),
            const SizedBox(height: 16),
            
            Text(
              widget.title,
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            
            Text(
              widget.description,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            
            if (_isRequesting)
              const CircularProgressIndicator()
            else
              ElevatedButton.icon(
                onPressed: _requestPermissions,
                icon: const Icon(Icons.security),
                label: const Text('منح الأذونات'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

// Widget مبسط لطلب أذونات الملفات
class FilePermissionWidget extends StatelessWidget {
  final VoidCallback? onPermissionGranted;

  const FilePermissionWidget({
    super.key,
    this.onPermissionGranted,
  });

  @override
  Widget build(BuildContext context) {
    return PermissionRequestWidget(
      title: 'الوصول للملفات',
      description: 'يحتاج التطبيق إلى إذن للوصول للملفات لتحميل وحفظ ملفات PDF',
      permissions: const [
        Permission.storage,
        Permission.manageExternalStorage,
        Permission.photos,
      ],
      onPermissionGranted: onPermissionGranted,
    );
  }
}
