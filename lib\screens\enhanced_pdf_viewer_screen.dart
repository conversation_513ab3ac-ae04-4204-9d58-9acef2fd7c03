import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_cached_pdfview/flutter_cached_pdfview.dart';
import 'package:provider/provider.dart';
import '../services/enhanced_pdf_service.dart';
import '../providers/theme_provider.dart';
import '../theme/app_theme.dart';

/// شاشة عرض PDF محسنة مع دعم Google Drive المباشر
class EnhancedPDFViewerScreen extends StatefulWidget {
  final String title;
  final String pdfUrl;
  final bool showDownloadButton;

  const EnhancedPDFViewerScreen({
    super.key,
    required this.title,
    required this.pdfUrl,
    this.showDownloadButton = true,
  });

  @override
  State<EnhancedPDFViewerScreen> createState() =>
      _EnhancedPDFViewerScreenState();
}

class _EnhancedPDFViewerScreenState extends State<EnhancedPDFViewerScreen> {
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;
  bool _isDownloading = false;
  double _downloadProgress = 0.0;
  bool _isFileDownloaded = false;
  bool _isLocalFile = false;

  // متحكمات العرض
  String? _currentPdfUrl;

  @override
  void initState() {
    super.initState();
    _initializePDFViewer();
    _checkIfFileDownloaded();
  }

  /// تهيئة عارض PDF
  Future<void> _initializePDFViewer() async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });

      // التحقق من الاتصال بالإنترنت
      final isConnected = await EnhancedPDFService.isConnected();

      if (!isConnected) {
        // محاولة فتح الملف المحلي إذا كان متاحاً
        final localPath = await EnhancedPDFService.getLocalFilePath(
          widget.title,
        );
        if (localPath != null) {
          await _loadLocalPDF(localPath);
          return;
        } else {
          throw Exception('لا يوجد اتصال بالإنترنت والملف غير متاح محلياً');
        }
      }

      // تحويل الرابط إلى رابط مباشر
      final directUrl = EnhancedPDFService.convertGoogleDriveUrl(widget.pdfUrl);

      if (kDebugMode) {
        print('🔗 الرابط المباشر: $directUrl');
      }

      // محاولة تحميل PDF مباشرة
      await _loadOnlinePDF(directUrl);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تهيئة عارض PDF: $e');
      }
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = e.toString();
      });
    }
  }

  /// تحميل PDF من الإنترنت مباشرة باستخدام flutter_cached_pdfview
  Future<void> _loadOnlinePDF(String url) async {
    try {
      // تحويل الرابط إلى رابط مباشر
      final directUrl = EnhancedPDFService.convertGoogleDriveUrl(url);

      setState(() {
        _currentPdfUrl = directUrl;
        _isLocalFile = false;
        _isLoading = false;
        _hasError = false;
      });

      if (kDebugMode) {
        print('✅ تم إعداد عرض PDF مباشرة من الإنترنت في الذاكرة فقط');
        print('🔗 الرابط المباشر: $directUrl');
      }
    } catch (e) {
      throw Exception('فشل في تحميل PDF مباشرة: $e');
    }
  }

  /// تحميل PDF محلي باستخدام flutter_cached_pdfview
  Future<void> _loadLocalPDF(String filePath) async {
    try {
      final file = File(filePath);

      // التحقق من وجود الملف
      if (!await file.exists()) {
        throw Exception('الملف غير موجود: $filePath');
      }

      // التحقق من حجم الملف
      final fileSize = await file.length();
      if (fileSize < 1024) {
        throw Exception('الملف تالف أو فارغ');
      }

      setState(() {
        _currentPdfUrl = filePath;
        _isLocalFile = true;
        _isLoading = false;
        _hasError = false;
      });

      if (kDebugMode) {
        print('✅ تم تحميل PDF من الملف المحلي: $filePath');
        print(
          '📊 حجم الملف: ${(fileSize / 1024 / 1024).toStringAsFixed(2)} MB',
        );
      }
    } catch (e) {
      throw Exception('فشل في تحميل PDF المحلي: $e');
    }
  }

  /// التحقق من وجود الملف محلياً
  Future<void> _checkIfFileDownloaded() async {
    final isDownloaded = await EnhancedPDFService.isFileDownloaded(
      widget.title,
    );
    setState(() {
      _isFileDownloaded = isDownloaded;
    });
  }

  /// تحميل الملف محلياً
  Future<void> _downloadPDF() async {
    if (_isDownloading) return;

    setState(() {
      _isDownloading = true;
      _downloadProgress = 0.0;
    });

    try {
      final filePath = await EnhancedPDFService.downloadPDFToLocal(
        url: widget.pdfUrl,
        fileName: widget.title,
        onProgress: (progress) {
          setState(() {
            _downloadProgress = progress;
          });
        },
      );

      if (filePath != null) {
        setState(() {
          _isFileDownloaded = true;
        });

        _showMessage('تم تحميل الملف بنجاح', isError: false);

        // إعادة تحميل من الملف المحلي
        await _loadLocalPDF(filePath);
      } else {
        _showMessage('فشل في تحميل الملف', isError: true);
      }
    } catch (e) {
      _showMessage('خطأ في التحميل: $e', isError: true);
    } finally {
      setState(() {
        _isDownloading = false;
        _downloadProgress = 0.0;
      });
    }
  }

  /// عرض رسالة للمستخدم
  void _showMessage(String message, {required bool isError}) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(color: Colors.white)),
        backgroundColor: isError ? Colors.red : Colors.green,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          backgroundColor:
              themeProvider.isDarkMode
                  ? const Color(0xFF0F172A)
                  : const Color(0xFFF8FAFC),
          appBar: _buildAppBar(themeProvider),
          body: _buildBody(),
          floatingActionButton:
              widget.showDownloadButton ? _buildDownloadFAB() : null,
        );
      },
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar(ThemeProvider themeProvider) {
    return AppBar(
      title: Text(
        widget.title,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
      backgroundColor: AppTheme.primaryColor,
      elevation: 0,
      iconTheme: const IconThemeData(color: Colors.white),
      actions: [
        // يمكن إضافة أزرار إضافية هنا لاحقاً
      ],
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    if (_isLoading) {
      return _buildLoadingView();
    }

    if (_hasError) {
      return _buildErrorView();
    }

    if (_currentPdfUrl != null) {
      return _buildPDFView();
    }

    return _buildErrorView();
  }

  /// عرض التحميل
  Widget _buildLoadingView() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
          SizedBox(height: 16),
          Text(
            'جاري تحميل الملف...',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  /// عرض الخطأ
  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red.shade400),
            const SizedBox(height: 16),
            Text(
              'فشل في تحميل الملف',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.red.shade400,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage ?? 'حدث خطأ غير متوقع',
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 14, color: Colors.grey),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _initializePDFViewer,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// عرض PDF باستخدام flutter_cached_pdfview
  Widget _buildPDFView() {
    if (_isLocalFile && _currentPdfUrl != null) {
      // عرض من ملف محلي
      return PDF(
        swipeHorizontal: false,
        autoSpacing: true,
        pageFling: true,
        onError: (error) {
          if (kDebugMode) {
            print('❌ خطأ في عرض PDF المحلي: $error');
          }
          setState(() {
            _hasError = true;
            _errorMessage = 'خطأ في عرض PDF: $error';
          });
        },
        onPageError: (page, error) {
          if (kDebugMode) {
            print('❌ خطأ في الصفحة $page: $error');
          }
        },
      ).fromPath(_currentPdfUrl!);
    } else if (_currentPdfUrl != null) {
      // عرض من الإنترنت مباشرة في الذاكرة فقط
      return PDF(
        swipeHorizontal: false,
        autoSpacing: true,
        pageFling: true,
        onError: (error) {
          if (kDebugMode) {
            print('❌ خطأ في عرض PDF من الإنترنت: $error');
          }
          setState(() {
            _hasError = true;
            _errorMessage = 'خطأ في عرض PDF: $error';
          });
        },
        onPageError: (page, error) {
          if (kDebugMode) {
            print('❌ خطأ في الصفحة $page: $error');
          }
        },
      ).fromUrl(_currentPdfUrl!);
    } else {
      return _buildErrorView();
    }
  }

  /// زر التحميل العائم
  Widget? _buildDownloadFAB() {
    if (_isFileDownloaded) {
      return FloatingActionButton(
        onPressed: null,
        backgroundColor: Colors.green,
        child: const Icon(Icons.check, color: Colors.white),
      );
    }

    if (_isDownloading) {
      return FloatingActionButton(
        onPressed: null,
        backgroundColor: AppTheme.primaryColor,
        child: CircularProgressIndicator(
          value: _downloadProgress,
          valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
          strokeWidth: 3,
        ),
      );
    }

    return FloatingActionButton(
      onPressed: _downloadPDF,
      backgroundColor: AppTheme.primaryColor,
      child: const Icon(Icons.download, color: Colors.white),
    );
  }
}
