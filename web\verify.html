<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفعيل الحساب - منصة الشريعة و القانون</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }

        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            max-width: 500px;
            width: 90%;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
        }

        .logo {
            font-size: 32px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 20px;
        }

        .success-icon {
            font-size: 64px;
            color: #4CAF50;
            margin-bottom: 20px;
        }

        .error-icon {
            font-size: 64px;
            color: #f44336;
            margin-bottom: 20px;
        }

        .loading-icon {
            font-size: 64px;
            color: #667eea;
            margin-bottom: 20px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 24px;
        }

        p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            transition: transform 0.2s;
        }

        .button:hover {
            transform: translateY(-2px);
        }

        .error-details {
            background: #ffebee;
            border: 1px solid #ffcdd2;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            color: #c62828;
            font-size: 14px;
        }

        .success-details {
            background: #e8f5e8;
            border: 1px solid #c8e6c9;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            color: #2e7d32;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">⚖️ منصة الشريعة و القانون</div>
        
        <div id="loading" style="display: block;">
            <div class="loading-icon">⏳</div>
            <h1>جاري التحقق من الرابط...</h1>
            <p>يرجى الانتظار بينما نقوم بتفعيل حسابك</p>
        </div>

        <div id="success" style="display: none;">
            <div class="success-icon">✅</div>
            <h1>تم تفعيل حسابك بنجاح!</h1>
            <div class="success-details">
                <p><strong>مرحباً بك في تطبيق منصة الشريعة و القانون!</strong></p>
                <p>تم إنشاء حسابك وتفعيله بنجاح. يمكنك الآن تسجيل الدخول إلى التطبيق.</p>
            </div>
            <p>يرجى العودة إلى التطبيق وتسجيل الدخول باستخدام بريدك الإلكتروني وكلمة المرور.</p>
            <a href="#" class="button" onclick="closeWindow()">إغلاق النافذة</a>
        </div>

        <div id="error" style="display: none;">
            <div class="error-icon">❌</div>
            <h1>فشل في تفعيل الحساب</h1>
            <div class="error-details">
                <p id="error-message">حدث خطأ أثناء تفعيل حسابك.</p>
            </div>
            <p>يرجى المحاولة مرة أخرى أو التواصل مع الدعم الفني.</p>
            <a href="#" class="button" onclick="retryVerification()">إعادة المحاولة</a>
            <a href="#" class="button" onclick="closeWindow()">إغلاق النافذة</a>
        </div>
    </div>

    <script type="module">
        // Import Firebase modules
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/9.0.0/firebase-app.js';
        import { getFirestore, doc, getDoc, updateDoc, setDoc } from 'https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore.js';
        import { getAuth, signInWithEmailLink, updateProfile, isSignInWithEmailLink } from 'https://www.gstatic.com/firebasejs/9.0.0/firebase-auth.js';

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyD__fwZYKuehthlJoqGKSDt0YN1TCsPUY8",
            authDomain: "legal2025.firebaseapp.com",
            projectId: "legal2025",
            storageBucket: "legal2025.firebasestorage.app",
            messagingSenderId: "801031214670",
            appId: "1:801031214670:web:a179401f6b476d34db551f"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);
        const auth = getAuth(app);

        // Get email from localStorage (stored when sending the link)
        let email = localStorage.getItem('emailForSignIn');

        // If not in localStorage, try URL parameters as fallback
        if (!email) {
            const urlParams = new URLSearchParams(window.location.search);
            email = urlParams.get('email');
        }

        // If still no email, prompt user
        if (!email) {
            email = prompt('يرجى إدخال بريدك الإلكتروني:');
        }

        // Verify the link using Firebase Auth Email Link
        async function verifyLink() {
            try {
                if (!email) {
                    throw new Error('رابط التحقق غير صالح أو ناقص');
                }

                // Get the full URL for Firebase Auth verification
                const emailLink = window.location.href;

                // Check if this is a valid sign-in link
                if (!isSignInWithEmailLink(auth, emailLink)) {
                    throw new Error('رابط التحقق غير صالح');
                }

                // Sign in with email link directly
                const result = await signInWithEmailLink(auth, email, emailLink);

                if (result.user) {
                    // Create user document in Firestore
                    const userDoc = {
                        uid: result.user.uid,
                        email: result.user.email,
                        displayName: result.user.displayName || 'مستخدم جديد',
                        emailVerified: true,
                        createdAt: new Date(),
                        loginProvider: 'email_link',
                        academicYear: 'السنة الأولى'
                    };

                    // Save to Firestore
                    await setDoc(doc(db, 'users', result.user.uid), userDoc);

                    // Clear email from localStorage
                    localStorage.removeItem('emailForSignIn');

                    // Success - show success message
                    showSuccess();
                } else {
                    throw new Error('فشل في تسجيل الدخول');
                }

            } catch (error) {
                console.error('Verification error:', error);
                showError(error.message || 'حدث خطأ أثناء التحقق من الرابط');
            }
        }

        // Simple hash function (for demo - use proper crypto in production)
        async function hashString(str) {
            const encoder = new TextEncoder();
            const data = encoder.encode(str);
            const hashBuffer = await crypto.subtle.digest('SHA-256', data);
            const hashArray = Array.from(new Uint8Array(hashBuffer));
            return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
        }

        function showSuccess() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error').style.display = 'none';
            document.getElementById('success').style.display = 'block';
        }

        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('success').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            document.getElementById('error-message').textContent = message;
        }

        // Global functions
        window.closeWindow = function() {
            window.close();
        };

        window.retryVerification = function() {
            location.reload();
        };

        // Start verification
        verifyLink();
    </script>
</body>
</html>
