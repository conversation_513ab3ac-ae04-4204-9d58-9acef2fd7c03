import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDarkMode ? const Color(0xFF1A1A1A) : Colors.grey[50],
      appBar: AppBar(
        title: Text(
          'سياسة الخصوصية',
          style: GoogleFonts.cairo(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: isDarkMode ? Colors.white : const Color(0xFF2C3E50),
          ),
        ),
        backgroundColor: isDarkMode ? const Color(0xFF2D2D2D) : Colors.white,
        elevation: 0,
        iconTheme: IconThemeData(
          color: isDarkMode ? Colors.white : const Color(0xFF2C3E50),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [const Color(0xFF1E3A8A), const Color(0xFF3B82F6)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(15),
                boxShadow: [
                  BoxShadow(
                    color: Colors.blue.withValues(alpha: 0.3),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.privacy_tip_rounded,
                    size: 50,
                    color: Colors.white,
                  ),
                  const SizedBox(height: 10),
                  Text(
                    'سياسة الخصوصية',
                    style: GoogleFonts.cairo(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 5),
                  Text(
                    'تطبيق منصة الشريعة و القانون',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      color: Colors.white.withValues(alpha: 0.9),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 25),

            // Last Updated
            Container(
              padding: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                color: isDarkMode ? const Color(0xFF2D2D2D) : Colors.blue[50],
                borderRadius: BorderRadius.circular(10),
                border: Border.all(
                  color: isDarkMode ? Colors.blue[700]! : Colors.blue[200]!,
                ),
              ),
              child: Row(
                children: [
                  Icon(Icons.update, color: Colors.blue[600], size: 20),
                  const SizedBox(width: 10),
                  Text(
                    'آخر تحديث: ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: isDarkMode ? Colors.blue[300] : Colors.blue[700],
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 25),

            // Content Sections
            _buildSection(
              context,
              '1. مقدمة',
              'نحن في تطبيق منصة الشريعة و القانون نحترم خصوصيتك ونلتزم بحماية معلوماتك الشخصية. توضح هذه السياسة كيفية جمع واستخدام وحماية معلوماتك عند استخدام تطبيقنا.',
              Icons.info_outline,
            ),

            _buildSection(
              context,
              '2. المعلومات التي نجمعها',
              '''• معلومات الحساب: الاسم، البريد الإلكتروني، السنة الدراسية
• معلومات الاستخدام: كيفية تفاعلك مع التطبيق والمحتوى
• معلومات الجهاز: نوع الجهاز، نظام التشغيل، معرف الجهاز
• المحتوى المُنشأ: المنشورات، التعليقات، الملفات المرفوعة''',
              Icons.data_usage,
            ),

            _buildSection(
              context,
              '3. كيفية استخدام المعلومات',
              '''• تقديم وتحسين خدمات التطبيق
• التواصل معك بشأن التحديثات والإشعارات
• ضمان أمان التطبيق ومنع الاستخدام غير المشروع
• تخصيص تجربتك في التطبيق
• الامتثال للمتطلبات القانونية''',
              Icons.settings_applications,
            ),

            _buildSection(
              context,
              '4. مشاركة المعلومات',
              '''نحن لا نبيع أو نؤجر معلوماتك الشخصية لأطراف ثالثة. قد نشارك معلوماتك في الحالات التالية:
• مع موافقتك الصريحة
• لحماية حقوقنا القانونية
• للامتثال للقوانين واللوائح
• مع مقدمي الخدمات الموثوقين (Firebase, Google)''',
              Icons.share,
            ),

            _buildSection(
              context,
              '5. أمان المعلومات',
              '''• تشفير البيانات أثناء النقل والتخزين
• استخدام خوادم آمنة ومحمية
• مراقبة مستمرة للأنشطة المشبوهة
• تحديثات أمنية منتظمة
• صلاحيات محدودة للوصول للبيانات''',
              Icons.security,
            ),

            _buildSection(context, '6. حقوقك', '''لديك الحق في:
• الوصول إلى معلوماتك الشخصية
• تصحيح أو تحديث معلوماتك
• حذف حسابك ومعلوماتك
• تقييد معالجة معلوماتك
• نقل معلوماتك إلى خدمة أخرى
• الاعتراض على معالجة معلوماتك''', Icons.account_circle),

            _buildSection(
              context,
              '7. ملفات تعريف الارتباط',
              '''نستخدم ملفات تعريف الارتباط وتقنيات مشابهة لـ:
• تحسين أداء التطبيق
• تذكر تفضيلاتك
• تحليل استخدام التطبيق
• تقديم محتوى مخصص''',
              Icons.cookie,
            ),

            _buildSection(
              context,
              '8. خدمات الطرف الثالث',
              '''نستخدم خدمات موثوقة مثل:
• Firebase (Google): لقاعدة البيانات والمصادقة
• Google Analytics: لتحليل الاستخدام
• Google Drive: لتخزين الملفات
جميع هذه الخدمات تلتزم بمعايير الخصوصية العالية''',
              Icons.cloud,
            ),

            _buildSection(
              context,
              '9. حماية الأطفال',
              '''تطبيقنا مخصص للطلاب الجامعيين (18+ سنة). إذا علمنا بجمع معلومات من أطفال دون 18 سنة، سنحذفها فوراً.''',
              Icons.child_care,
            ),

            _buildSection(
              context,
              '10. التغييرات على السياسة',
              '''قد نحدث هذه السياسة من وقت لآخر. سنخطرك بأي تغييرات مهمة عبر:
• إشعار في التطبيق
• رسالة بريد إلكتروني
• تحديث تاريخ "آخر تحديث"''',
              Icons.update,
            ),

            _buildSection(
              context,
              '11. التواصل معنا',
              '''إذا كان لديك أي أسئلة حول سياسة الخصوصية:
📧 البريد الإلكتروني: <EMAIL>
📱 التطبيق: استخدم خاصية "اتصل بنا"
🏢 المؤسسة:كلية الشريعة والقانون - جامعة أسيوط''',
              Icons.contact_mail,
            ),

            const SizedBox(height: 30),

            // Footer
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: isDarkMode ? const Color(0xFF2D2D2D) : Colors.grey[100],
                borderRadius: BorderRadius.circular(15),
                border: Border.all(
                  color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
                ),
              ),
              child: Column(
                children: [
                  Icon(Icons.verified_user, size: 40, color: Colors.green[600]),
                  const SizedBox(height: 10),
                  Text(
                    'نلتزم بحماية خصوصيتك',
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color:
                          isDarkMode ? Colors.white : const Color(0xFF2C3E50),
                    ),
                  ),
                  const SizedBox(height: 5),
                  Text(
                    'تطبيق منصة الشريعة و القانون - آمن وموثوق',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(
    BuildContext context,
    String title,
    String content,
    IconData icon,
  ) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF2D2D2D) : Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color:
                isDarkMode
                    ? Colors.black26
                    : Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: Colors.blue[700], size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : const Color(0xFF2C3E50),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 15),
          Text(
            content,
            style: GoogleFonts.cairo(
              fontSize: 15,
              height: 1.6,
              color: isDarkMode ? Colors.grey[300] : Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }
}
