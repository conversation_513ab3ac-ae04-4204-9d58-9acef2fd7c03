import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:crypto/crypto.dart';

/// خدمة حفظ حالة تسجيل الدخول بشكل آمن
class AuthPersistenceService {
  static const String _isLoggedInKey = 'is_logged_in';
  static const String _userDataKey = 'user_data';
  static const String _rememberMeKey = 'remember_me';
  static const String _lastLoginKey = 'last_login';
  static const String _sessionTokenKey = 'session_token';

  static SharedPreferences? _prefs;
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(encryptedSharedPreferences: true),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  /// تهيئة الخدمة
  static Future<void> initialize() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  /// حفظ حالة تسجيل الدخول
  static Future<void> saveLoginState({
    required String userId,
    required String email,
    required String displayName,
    required bool rememberMe,
    Map<String, dynamic>? additionalData,
  }) async {
    await initialize();

    try {
      // حفظ حالة تسجيل الدخول
      await _prefs!.setBool(_isLoggedInKey, true);
      await _prefs!.setBool(_rememberMeKey, rememberMe);
      await _prefs!.setString(_lastLoginKey, DateTime.now().toIso8601String());

      // إنشاء session token آمن
      String sessionToken = _generateSessionToken(userId, email);
      await _secureStorage.write(key: _sessionTokenKey, value: sessionToken);

      // حفظ بيانات المستخدم بشكل آمن
      Map<String, dynamic> userData = {
        'userId': userId,
        'email': email,
        'displayName': displayName,
        'loginTime': DateTime.now().toIso8601String(),
        'sessionToken': sessionToken,
        ...?additionalData,
      };

      String encryptedUserData = _encryptData(jsonEncode(userData));
      await _secureStorage.write(key: _userDataKey, value: encryptedUserData);

      debugPrint('✅ تم حفظ حالة تسجيل الدخول بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ حالة تسجيل الدخول: $e');
    }
  }

  /// التحقق من حالة تسجيل الدخول المحفوظة
  static Future<LoginState> getLoginState() async {
    await initialize();

    try {
      bool isLoggedIn = _prefs!.getBool(_isLoggedInKey) ?? false;
      bool rememberMe = _prefs!.getBool(_rememberMeKey) ?? false;

      if (!isLoggedIn || !rememberMe) {
        return LoginState.notLoggedIn();
      }

      // التحقق من صحة الجلسة
      String? encryptedUserData = await _secureStorage.read(key: _userDataKey);
      String? sessionToken = await _secureStorage.read(key: _sessionTokenKey);

      if (sessionToken == null) {
        await clearLoginState();
        return LoginState.notLoggedIn();
      }

      // فك تشفير بيانات المستخدم
      String decryptedData = _decryptData(encryptedUserData!);
      Map<String, dynamic> userData = jsonDecode(decryptedData);

      // التحقق من صحة session token
      String expectedToken = _generateSessionToken(
        userData['userId'],
        userData['email'],
      );

      if (sessionToken != expectedToken) {
        await clearLoginState();
        return LoginState.notLoggedIn();
      }

      // التحقق من انتهاء صلاحية الجلسة (30 يوم)
      DateTime loginTime = DateTime.parse(userData['loginTime']);
      if (DateTime.now().difference(loginTime).inDays > 30) {
        await clearLoginState();
        return LoginState.expired();
      }

      return LoginState.loggedIn(userData);
    } catch (e) {
      debugPrint('❌ خطأ في قراءة حالة تسجيل الدخول: $e');
      await clearLoginState();
      return LoginState.notLoggedIn();
    }
  }

  /// مسح حالة تسجيل الدخول
  static Future<void> clearLoginState() async {
    await initialize();

    try {
      await _prefs!.remove(_isLoggedInKey);
      await _prefs!.remove(_rememberMeKey);
      await _prefs!.remove(_lastLoginKey);
      await _secureStorage.delete(key: _userDataKey);
      await _secureStorage.delete(key: _sessionTokenKey);

      debugPrint('✅ تم مسح حالة تسجيل الدخول');
    } catch (e) {
      debugPrint('❌ خطأ في مسح حالة تسجيل الدخول: $e');
    }
  }

  /// تحديث آخر وقت نشاط
  static Future<void> updateLastActivity() async {
    await initialize();

    try {
      await _prefs!.setString(
        'last_activity',
        DateTime.now().toIso8601String(),
      );
    } catch (e) {
      debugPrint('❌ خطأ في تحديث آخر نشاط: $e');
    }
  }

  /// التحقق من Remember Me
  static Future<bool> isRememberMeEnabled() async {
    await initialize();
    return _prefs!.getBool(_rememberMeKey) ?? false;
  }

  /// تحديث إعداد Remember Me
  static Future<void> setRememberMe(bool enabled) async {
    await initialize();
    await _prefs!.setBool(_rememberMeKey, enabled);
  }

  /// إنشاء session token آمن
  static String _generateSessionToken(String userId, String email) {
    String data = '$userId:$email:${DateTime.now().millisecondsSinceEpoch}';
    var bytes = utf8.encode(data);
    var digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// تشفير البيانات (تشفير بسيط)
  static String _encryptData(String data) {
    // تشفير بسيط باستخدام Base64 + XOR
    List<int> bytes = utf8.encode(data);
    List<int> key = utf8.encode('legal2025_secret_key');

    for (int i = 0; i < bytes.length; i++) {
      bytes[i] ^= key[i % key.length];
    }

    return base64Encode(bytes);
  }

  /// فك تشفير البيانات
  static String _decryptData(String encryptedData) {
    List<int> bytes = base64Decode(encryptedData);
    List<int> key = utf8.encode('legal2025_secret_key');

    for (int i = 0; i < bytes.length; i++) {
      bytes[i] ^= key[i % key.length];
    }

    return utf8.decode(bytes);
  }

  /// الحصول على معلومات الجلسة
  static Future<SessionInfo?> getSessionInfo() async {
    LoginState state = await getLoginState();

    if (state.isLoggedIn && state.userData != null) {
      return SessionInfo(
        userId: state.userData!['userId'],
        email: state.userData!['email'],
        displayName: state.userData!['displayName'],
        loginTime: DateTime.parse(state.userData!['loginTime']),
        isRememberMeEnabled: await isRememberMeEnabled(),
      );
    }

    return null;
  }

  /// تنظيف البيانات المنتهية الصلاحية
  static Future<void> cleanupExpiredData() async {
    await initialize();

    try {
      LoginState state = await getLoginState();
      if (state.isExpired) {
        await clearLoginState();
        debugPrint('🧹 تم تنظيف البيانات المنتهية الصلاحية');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف البيانات: $e');
    }
  }
}

/// حالة تسجيل الدخول
class LoginState {
  final bool isLoggedIn;
  final bool isExpired;
  final Map<String, dynamic>? userData;

  LoginState._({
    required this.isLoggedIn,
    required this.isExpired,
    this.userData,
  });

  factory LoginState.loggedIn(Map<String, dynamic> userData) {
    return LoginState._(isLoggedIn: true, isExpired: false, userData: userData);
  }

  factory LoginState.notLoggedIn() {
    return LoginState._(isLoggedIn: false, isExpired: false);
  }

  factory LoginState.expired() {
    return LoginState._(isLoggedIn: false, isExpired: true);
  }
}

/// معلومات الجلسة
class SessionInfo {
  final String userId;
  final String email;
  final String displayName;
  final DateTime loginTime;
  final bool isRememberMeEnabled;

  SessionInfo({
    required this.userId,
    required this.email,
    required this.displayName,
    required this.loginTime,
    required this.isRememberMeEnabled,
  });

  Duration get sessionDuration => DateTime.now().difference(loginTime);
  bool get isSessionValid => sessionDuration.inDays <= 30;
}
