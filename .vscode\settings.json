{
    "diffEditor.codeLens": true,

    // تعطيل Java Extension تماماً لمشاريع Flutter
    "java.configuration.updateBuildConfiguration": "disabled",
    "java.enabled": false,
    "java.import.gradle.enabled": false,
    "java.import.maven.enabled": false,
    "java.autobuild.enabled": false,
    "java.server.launchMode": "Disabled",
    "redhat.java.enabled": false,
    "redhat.telemetry.enabled": false,

    // منع Java Extension من معالجة مجلد android
    "java.import.exclusions": [
        "**/android/**",
        "**/ios/**",
        "**/build/**",
        "**/.dart_tool/**",
        "**/node_modules/**"
    ],

    // إعدادات Flutter فقط
    "dart.enableSdkFormatter": true,
    "dart.lineLength": 80,

    // إخفاء ملفات Java/Eclipse
    "files.exclude": {
        "**/.dart_tool": true,
        "**/build": true,
        "**/.gradle": true,
        "**/.settings": true,
        "**/.project": true,
        "**/.classpath": true,
        "**/.metadata": true
    },

    // منع مراقبة ملفات Java
    "files.watcherExclude": {
        "**/.dart_tool/**": true,
        "**/build/**": true,
        "**/.gradle/**": true,
        "**/android/.gradle/**": true,
        "**/android/build/**": true,
        "**/android/.settings/**": true,
        "**/android/.metadata/**": true
    },

    // منع البحث في ملفات Java
    "search.exclude": {
        "**/build": true,
        "**/.dart_tool": true,
        "**/.gradle": true,
        "**/android/.gradle": true,
        "**/android/build": true,
        "**/android/.settings": true
    }
}